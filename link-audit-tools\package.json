{"name": "sanitation-app-link-audit", "version": "1.0.0", "description": "Automated link checking tools for the Sanitation Checklist Application", "main": "link-checker.js", "scripts": {"audit": "node link-checker.js", "dashboard": "node status-dashboard.js", "install-deps": "npm install", "test": "node test-link-checker.js", "monitor": "node automated-monitor.js start", "monitor:stop": "node automated-monitor.js stop", "monitor:status": "node automated-monitor.js status", "fix": "node fix-broken-endpoints.js apply", "fix:list": "node fix-broken-endpoints.js list", "extract": "node static-link-extractor.js"}, "dependencies": {"puppeteer": "^21.0.0", "express": "^4.18.2", "cors": "^2.8.5", "csv-parser": "^3.0.0", "node-fetch": "^2.6.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["link-audit", "testing", "automation", "sanitation-app"], "author": "Sanitation App Team", "license": "MIT"}