/**
 * Simple CSV to Excel converter
 */
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

// Input and output file paths
const resultsDir = path.join(__dirname, 'link-audit-results');
const inputCsvPath = path.join(resultsDir, 'link-audit-results.csv');
const outputXlsxPath = path.join(resultsDir, 'link-audit-results.xlsx');

// Ensure results directory exists
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// Function to convert CSV to XLSX
function convertCsvToXlsx() {
  console.log('📊 Converting CSV to Excel...');
  
  try {
    // Check if CSV file exists
    if (!fs.existsSync(inputCsvPath)) {
      console.error(`❌ CSV file not found: ${inputCsvPath}`);
      console.log('Please run the link audit first: npm run audit');
      return false;
    }
    
    // Read the CSV file
    console.log('Reading CSV data...');
    const csvData = fs.readFileSync(inputCsvPath, 'utf8');
    
    // Create a workbook from the CSV
    const workbook = xlsx.read(csvData, { type: 'string' });
    
    // Write to Excel file
    xlsx.writeFile(workbook, outputXlsxPath);
    
    console.log(`✅ Conversion successful! Excel file saved to: ${outputXlsxPath}`);
    return true;
  } catch (error) {
    console.error('❌ Error converting CSV to Excel:', error);
    console.error(error.stack);
    return false;
  }
}

// Run the conversion if called directly
if (require.main === module) {
  convertCsvToXlsx();
}

module.exports = { convertCsvToXlsx };