<%- include('../layouts/main') %>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
        .audit-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .audit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e0e0e0;
        }

        .audit-title {
            font-size: 2rem;
            color: #2c3e50;
            margin: 0;
        }

        .audit-filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .filter-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
        }

        .filter-input {
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .audit-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .audit-table th {
            background: #343a40;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .audit-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .audit-table tr:hover {
            background: #f8f9fa;
        }

        .action-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .action-submission { background: #d4edda; color: #155724; }
        .action-validation { background: #d1ecf1; color: #0c5460; }
        .action-system { background: #f8d7da; color: #721c24; }
        .action-user { background: #fff3cd; color: #856404; }

        .timestamp {
            font-family: monospace;
            font-size: 0.875rem;
            color: #6c757d;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-style: italic;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid #dee2e6;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
    </style>

    <div class="audit-container">
        <div class="audit-header">
            <h1 class="audit-title">🔍 Audit Trail</h1>
            <div>
                <a href="/compliance" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="audit-filters">
            <div class="filter-group">
                <label class="filter-label">Days</label>
                <select id="daysFilter" class="filter-input">
                    <option value="7">Last 7 days</option>
                    <option value="30" selected>Last 30 days</option>
                    <option value="90">Last 90 days</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">Action Type</label>
                <select id="actionTypeFilter" class="filter-input">
                    <option value="">All Actions</option>
                    <option value="SUBMITTED">Submissions</option>
                    <option value="VALIDATED">Validations</option>
                    <option value="LOGIN">User Logins</option>
                    <option value="SYSTEM">System Events</option>
                </select>
            </div>
            <div class="filter-group">
                <label class="filter-label">User ID</label>
                <input type="text" id="userIdFilter" class="filter-input" placeholder="Filter by user ID">
            </div>
            <div class="filter-group">
                <label class="filter-label">&nbsp;</label>
                <button onclick="loadAuditTrail()" class="btn btn-primary">
                    <i class="fas fa-search"></i> Apply Filters
                </button>
            </div>
        </div>

        <!-- Audit Trail Table -->
        <div id="auditTrailContent">
            <div class="loading">Loading audit trail...</div>
        </div>
    </div>

    <script>
        let backendApiUrl = 'http://localhost:3001';

        // Load audit trail on page load
        document.addEventListener('DOMContentLoaded', async function() {
            const authenticated = await ensureAuthentication();
            if (authenticated) {
                loadAuditTrail();
            } else {
                console.warn('User not authenticated for API calls');
            }
        });

        // Function to ensure user is authenticated and get JWT token
        async function ensureAuthentication() {
            try {
                const response = await fetch('/api/auth/issue-jwt-for-session', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.token) {
                        localStorage.setItem('jwtToken', data.token);
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Authentication check failed:', error);
                return false;
            }
        }

        // Load audit trail data
        async function loadAuditTrail() {
            const token = localStorage.getItem('jwtToken');
            if (!token) {
                console.error('No JWT token available');
                return;
            }

            const days = document.getElementById('daysFilter').value;
            const actionType = document.getElementById('actionTypeFilter').value;
            const userId = document.getElementById('userIdFilter').value;

            let url = `${backendApiUrl}/api/compliance/audit-trail?days=${days}&limit=100`;
            if (actionType) url += `&actionType=${encodeURIComponent(actionType)}`;
            if (userId) url += `&targetUserId=${encodeURIComponent(userId)}`;

            try {
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAuditTrail(data.auditTrail);
                } else {
                    console.error('Failed to load audit trail:', response.status);
                    showAuditError();
                }
            } catch (error) {
                console.error('Error loading audit trail:', error);
                showAuditError();
            }
        }

        // Display audit trail
        function displayAuditTrail(auditTrail) {
            const container = document.getElementById('auditTrailContent');
            
            if (auditTrail.length === 0) {
                container.innerHTML = '<div class="no-data">No audit trail entries found for the selected criteria.</div>';
                return;
            }

            let tableHTML = `
                <table class="audit-table">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>Action</th>
                            <th>User ID</th>
                            <th>Checklist</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            auditTrail.forEach(entry => {
                const timestamp = new Date(entry.timestamp).toLocaleString();
                const actionClass = getActionClass(entry.action_type);
                const details = entry.details ? JSON.stringify(entry.details).substring(0, 100) + '...' : '';
                
                tableHTML += `
                    <tr>
                        <td class="timestamp">${timestamp}</td>
                        <td><span class="action-badge ${actionClass}">${entry.action_type}</span></td>
                        <td>${entry.user_id || 'System'}</td>
                        <td>${entry.checklist_title || entry.original_checklist_filename || '-'}</td>
                        <td title="${entry.details ? JSON.stringify(entry.details) : ''}">${details}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        // Get CSS class for action type
        function getActionClass(actionType) {
            if (actionType.includes('SUBMIT')) return 'action-submission';
            if (actionType.includes('VALID')) return 'action-validation';
            if (actionType.includes('SYSTEM') || actionType.includes('SERVER')) return 'action-system';
            return 'action-user';
        }

        // Show error state
        function showAuditError() {
            document.getElementById('auditTrailContent').innerHTML = 
                '<div class="error">Failed to load audit trail data. Please try again later.</div>';
        }
    </script>
