const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureCompliance } = require('../middleware/authMiddleware');

// Main compliance dashboard
router.get('/', ensureAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance] User accessing compliance dashboard:', req.user ? req.user.username : 'No user');
  res.render('compliance/dashboard', {
    title: 'Compliance Dashboard',
    user: req.user
  });
});

// Debug route to check if compliance routes are working
router.get('/debug', (req, res) => {
  console.log('[Compliance Debug] User accessing debug route:', req.user ? req.user.username : 'No user');
  res.json({
    message: 'Compliance routes are working',
    user: req.user ? {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    } : null,
    authenticated: req.isAuthenticated()
  });
});

// Compliance metrics page - ensure this route is correctly defined
router.get('/metrics', ensureAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance Metrics] User accessing compliance metrics:', req.user ? req.user.username : 'No user');
  try {
    // Render the metrics view
    res.render('compliance/metrics', {
      title: 'Compliance Metrics',
      user: req.user,
      backendApiUrl: process.env.BACKEND_API_URL || 'http://localhost:3001'
    });
  } catch (error) {
    console.error('[Compliance Metrics] Error rendering metrics:', error);
    req.flash('error', 'Failed to load compliance metrics: ' + error.message);
    res.redirect('/compliance');
  }
});

// Test route that only checks authentication, not role
router.get('/test-auth', ensureAuthenticated, (req, res) => {
  res.json({
    message: 'Authentication check passed',
    user: {
      username: req.user.username,
      role: req.user.role,
      isAdmin: req.user.isAdmin
    }
  });
});

// Export the router
module.exports = router;
