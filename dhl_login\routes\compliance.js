const express = require('express');
const router = express.Router();
const { ensureCompliance } = require('../middleware/authMiddleware');

// Authentication middleware for web pages
const ensureWebAuthenticated = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  req.session.returnTo = req.originalUrl;
  req.flash('error', 'Please log in to view that resource.');
  res.redirect('/login-page');
};

// Main compliance dashboard
router.get('/', ensureWebAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance] User accessing compliance dashboard:', req.user ? req.user.username : 'No user');
  res.render('compliance/dashboard', {
    title: 'Compliance Dashboard',
    user: req.user
  });
});

// Debug route to check if compliance routes are working
router.get('/debug', (req, res) => {
  console.log('[Compliance Debug] User accessing debug route:', req.user ? req.user.username : 'No user');
  res.json({
    message: 'Compliance routes are working',
    user: req.user ? {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    } : null,
    authenticated: req.isAuthenticated()
  });
});

// Compliance metrics page - ensure this route is correctly defined
router.get('/metrics', ensureWebAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance Metrics] User accessing compliance metrics:', req.user ? req.user.username : 'No user');
  try {
    // Render the metrics view
    res.render('compliance/metrics', {
      title: 'Compliance Metrics',
      user: req.user,
      backendApiUrl: process.env.BACKEND_API_URL || 'http://localhost:3001'
    });
  } catch (error) {
    console.error('[Compliance Metrics] Error rendering metrics:', error);
    req.flash('error', 'Failed to load compliance metrics: ' + error.message);
    res.redirect('/compliance');
  }
});

// Audit trail page
router.get('/audit', ensureWebAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance Audit] User accessing audit trail:', req.user ? req.user.username : 'No user');
  try {
    res.render('compliance/audit', {
      title: 'Audit Trail',
      user: req.user
    });
  } catch (error) {
    console.error('[Compliance Audit] Error rendering audit:', error);
    res.status(500).send('Error rendering audit trail: ' + error.message);
  }
});

// Non-compliance reports page
router.get('/non-compliance', ensureWebAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance Non-Compliance] User accessing non-compliance reports:', req.user ? req.user.username : 'No user');
  try {
    res.render('compliance/non-compliance', {
      title: 'Non-Compliance Reports',
      user: req.user
    });
  } catch (error) {
    console.error('[Compliance Non-Compliance] Error rendering non-compliance:', error);
    res.status(500).send('Error rendering non-compliance reports: ' + error.message);
  }
});

// Validation trends page
router.get('/validation-trends', ensureWebAuthenticated, ensureCompliance, (req, res) => {
  console.log('[Compliance Validation Trends] User accessing validation trends:', req.user ? req.user.username : 'No user');
  try {
    res.render('compliance/validation-trends', {
      title: 'Validation Trends',
      user: req.user
    });
  } catch (error) {
    console.error('[Compliance Validation Trends] Error rendering validation trends:', error);
    res.status(500).send('Error rendering validation trends: ' + error.message);
  }
});

// Test route that only checks authentication, not role
router.get('/test-auth', ensureWebAuthenticated, (req, res) => {
  res.json({
    message: 'Authentication check passed',
    user: {
      username: req.user.username,
      role: req.user.role,
      isAdmin: req.user.isAdmin
    }
  });
});

// Export the router
module.exports = router;
