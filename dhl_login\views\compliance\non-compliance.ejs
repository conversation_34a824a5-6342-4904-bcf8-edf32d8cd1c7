<%- include('../layouts/main') %>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
        .non-compliance-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e0e0e0;
        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin: 0;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #e74c3c;
        }

        .summary-card.warning {
            border-left-color: #f39c12;
        }

        .summary-card.critical {
            border-left-color: #e74c3c;
        }

        .summary-card.info {
            border-left-color: #3498db;
        }

        .card-title {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0 0 0.5rem 0;
            text-transform: uppercase;
            font-weight: 600;
        }

        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .filters-row {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 150px;
        }

        .filter-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
        }

        .filter-input {
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .non-compliance-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .non-compliance-table th {
            background: #343a40;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .non-compliance-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .non-compliance-table tr:hover {
            background: #f8f9fa;
        }

        .severity-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .severity-high { background: #f8d7da; color: #721c24; }
        .severity-medium { background: #fff3cd; color: #856404; }
        .severity-low { background: #d1ecf1; color: #0c5460; }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-open { background: #f8d7da; color: #721c24; }
        .status-investigating { background: #fff3cd; color: #856404; }
        .status-resolved { background: #d4edda; color: #155724; }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.75rem; }

        .btn:hover { opacity: 0.9; }

        .loading, .no-data, .error {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-radius: 4px;
        }

        .task-details {
            font-size: 0.875rem;
            color: #6c757d;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .timestamp {
            font-family: monospace;
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>

    <div class="non-compliance-container">
        <div class="page-header">
            <h1 class="page-title">⚠️ Non-Compliance Reports</h1>
            <div>
                <a href="/compliance" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card critical">
                <h3 class="card-title">Critical Issues</h3>
                <p class="card-value" id="criticalIssues">--</p>
            </div>
            <div class="summary-card warning">
                <h3 class="card-title">Open Issues</h3>
                <p class="card-value" id="openIssues">--</p>
            </div>
            <div class="summary-card info">
                <h3 class="card-title">Total Non-Compliant Tasks</h3>
                <p class="card-value" id="totalNonCompliant">--</p>
            </div>
            <div class="summary-card info">
                <h3 class="card-title">Compliance Rate</h3>
                <p class="card-value" id="complianceRate">--%</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filters-row">
                <div class="filter-group">
                    <label class="filter-label">Time Period</label>
                    <select id="daysFilter" class="filter-input">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Severity</label>
                    <select id="severityFilter" class="filter-input">
                        <option value="">All Severities</option>
                        <option value="HIGH">High</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="LOW">Low</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Checklist Type</label>
                    <select id="checklistFilter" class="filter-input">
                        <option value="">All Checklists</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">&nbsp;</label>
                    <button onclick="loadNonComplianceData()" class="btn btn-primary">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Non-Compliance Table -->
        <div id="nonComplianceContent">
            <div class="loading">Loading non-compliance data...</div>
        </div>
    </div>

    <script>
        let backendApiUrl = 'http://localhost:3001';

        // Load data on page load
        document.addEventListener('DOMContentLoaded', async function() {
            const authenticated = await ensureAuthentication();
            if (authenticated) {
                loadNonComplianceData();
            } else {
                console.warn('User not authenticated for API calls');
            }
        });

        // Function to ensure user is authenticated and get JWT token
        async function ensureAuthentication() {
            try {
                const response = await fetch('/api/auth/issue-jwt-for-session', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.token) {
                        localStorage.setItem('jwtToken', data.token);
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Authentication check failed:', error);
                return false;
            }
        }

        // Load non-compliance data
        async function loadNonComplianceData() {
            const token = localStorage.getItem('jwtToken');
            if (!token) {
                console.error('No JWT token available');
                return;
            }

            const days = document.getElementById('daysFilter').value;
            const severity = document.getElementById('severityFilter').value;
            const checklist = document.getElementById('checklistFilter').value;

            let url = `${backendApiUrl}/api/compliance/non-compliance?days=${days}`;
            if (severity) url += `&severity=${encodeURIComponent(severity)}`;
            if (checklist) url += `&checklistType=${encodeURIComponent(checklist)}`;

            try {
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateSummaryCards(data);
                    displayNonComplianceTable(data.nonCompliantTasks);
                } else {
                    console.error('Failed to load non-compliance data:', response.status);
                    showError();
                }
            } catch (error) {
                console.error('Error loading non-compliance data:', error);
                showError();
            }
        }

        // Update summary cards
        function updateSummaryCards(data) {
            document.getElementById('criticalIssues').textContent = data.criticalIssues || 0;
            document.getElementById('openIssues').textContent = data.openIssues || 0;
            document.getElementById('totalNonCompliant').textContent = data.totalNonCompliant || 0;
            document.getElementById('complianceRate').textContent = 
                data.complianceRate ? `${data.complianceRate}%` : '--';
        }

        // Display non-compliance table
        function displayNonComplianceTable(nonCompliantTasks) {
            const container = document.getElementById('nonComplianceContent');
            
            if (!nonCompliantTasks || nonCompliantTasks.length === 0) {
                container.innerHTML = '<div class="no-data">No non-compliance issues found for the selected criteria.</div>';
                return;
            }

            let tableHTML = `
                <table class="non-compliance-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Checklist</th>
                            <th>Task</th>
                            <th>Submitted By</th>
                            <th>Severity</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            nonCompliantTasks.forEach(task => {
                const date = new Date(task.submission_timestamp).toLocaleDateString();
                const severity = task.severity || 'MEDIUM';
                const status = task.status || 'OPEN';
                
                tableHTML += `
                    <tr>
                        <td class="timestamp">${date}</td>
                        <td>${task.original_checklist_filename || 'Unknown'}</td>
                        <td class="task-details" title="${task.task_description || task.task_text || ''}">${(task.task_description || task.task_text || 'Task details unavailable').substring(0, 50)}...</td>
                        <td>${task.submitted_by_user_id || 'Unknown'}</td>
                        <td><span class="severity-badge severity-${severity.toLowerCase()}">${severity}</span></td>
                        <td><span class="status-badge status-${status.toLowerCase()}">${status}</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary" onclick="viewDetails('${task.submission_id}', '${task.task_id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        // View task details
        function viewDetails(submissionId, taskId) {
            // This would typically open a modal or navigate to a detailed view
            alert(`View details for Submission: ${submissionId}, Task: ${taskId}`);
        }

        // Show error state
        function showError() {
            document.getElementById('nonComplianceContent').innerHTML = 
                '<div class="error">Failed to load non-compliance data. Please try again later.</div>';
            
            // Reset summary cards
            document.getElementById('criticalIssues').textContent = 'Error';
            document.getElementById('openIssues').textContent = 'Error';
            document.getElementById('totalNonCompliant').textContent = 'Error';
            document.getElementById('complianceRate').textContent = 'Error';
        }
    </script>
