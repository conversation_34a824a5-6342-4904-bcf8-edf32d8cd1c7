<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
        .validation-trends-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e0e0e0;
        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin: 0;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        .metric-card.success {
            border-left-color: #27ae60;
        }

        .metric-card.warning {
            border-left-color: #f39c12;
        }

        .metric-card.danger {
            border-left-color: #e74c3c;
        }

        .metric-title {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0 0 0.5rem 0;
            text-transform: uppercase;
            font-weight: 600;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }

        .metric-subtitle {
            font-size: 0.75rem;
            color: #6c757d;
            margin: 0.25rem 0 0 0;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0 0 1rem 0;
        }

        .chart-canvas {
            max-height: 300px;
        }

        .filters-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .filters-row {
            display: flex;
            gap: 1rem;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            min-width: 150px;
        }

        .filter-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #495057;
        }

        .filter-input {
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.875rem;
        }

        .trends-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 2rem;
        }

        .trends-table th {
            background: #343a40;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .trends-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        .trends-table tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }

        .loading, .no-data, .error {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-radius: 4px;
        }

        .performance-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .performance-excellent { background: #27ae60; }
        .performance-good { background: #f39c12; }
        .performance-poor { background: #e74c3c; }

        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .filters-row {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>

    <div class="validation-trends-container">
        <div class="page-header">
            <h1 class="page-title">📈 Validation Trends</h1>
            <div>
                <a href="/compliance" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-grid">
            <div class="metric-card success">
                <h3 class="metric-title">Average Validation Time</h3>
                <p class="metric-value" id="avgValidationTime">--</p>
                <p class="metric-subtitle">hours</p>
            </div>
            <div class="metric-card">
                <h3 class="metric-title">Total Validations</h3>
                <p class="metric-value" id="totalValidations">--</p>
                <p class="metric-subtitle">this period</p>
            </div>
            <div class="metric-card warning">
                <h3 class="metric-title">Validation Success Rate</h3>
                <p class="metric-value" id="validationSuccessRate">--%</p>
                <p class="metric-subtitle">approved validations</p>
            </div>
            <div class="metric-card">
                <h3 class="metric-title">Active Supervisors</h3>
                <p class="metric-value" id="activeSupervisors">--</p>
                <p class="metric-subtitle">performing validations</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filters-row">
                <div class="filter-group">
                    <label class="filter-label">Time Period</label>
                    <select id="daysFilter" class="filter-input">
                        <option value="7">Last 7 days</option>
                        <option value="30" selected>Last 30 days</option>
                        <option value="90">Last 90 days</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Supervisor</label>
                    <select id="supervisorFilter" class="filter-input">
                        <option value="">All Supervisors</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">&nbsp;</label>
                    <button onclick="loadValidationTrends()" class="btn btn-primary">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="charts-section">
            <div class="chart-container">
                <h3 class="chart-title">Validation Volume Over Time</h3>
                <canvas id="validationVolumeChart" class="chart-canvas"></canvas>
            </div>
            <div class="chart-container">
                <h3 class="chart-title">Average Validation Time Trend</h3>
                <canvas id="validationTimeChart" class="chart-canvas"></canvas>
            </div>
        </div>

        <!-- Supervisor Performance Table -->
        <div id="supervisorPerformanceContent">
            <div class="loading">Loading supervisor performance data...</div>
        </div>
    </div>

    <script>
        let backendApiUrl = 'http://localhost:3001';
        let validationVolumeChart = null;
        let validationTimeChart = null;

        // Load data on page load
        document.addEventListener('DOMContentLoaded', async function() {
            const authenticated = await ensureAuthentication();
            if (authenticated) {
                loadValidationTrends();
            } else {
                console.warn('User not authenticated for API calls');
            }
        });

        // Function to ensure user is authenticated and get JWT token
        async function ensureAuthentication() {
            try {
                const response = await fetch('/api/auth/issue-jwt-for-session', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.token) {
                        localStorage.setItem('jwtToken', data.token);
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Authentication check failed:', error);
                return false;
            }
        }

        // Load validation trends data
        async function loadValidationTrends() {
            const token = localStorage.getItem('jwtToken');
            if (!token) {
                console.error('No JWT token available');
                return;
            }

            const days = document.getElementById('daysFilter').value;
            const supervisor = document.getElementById('supervisorFilter').value;

            let url = `${backendApiUrl}/api/analytics/validation-turnaround?days=${days}`;
            if (supervisor) url += `&supervisor=${encodeURIComponent(supervisor)}`;

            try {
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateMetrics(data);
                    createCharts(data);
                    displaySupervisorPerformance(data.turnaroundData);
                } else {
                    console.error('Failed to load validation trends:', response.status);
                    showError();
                }
            } catch (error) {
                console.error('Error loading validation trends:', error);
                showError();
            }
        }

        // Update metrics
        function updateMetrics(data) {
            const avgTime = data.turnaroundData && data.turnaroundData.length > 0 
                ? (data.turnaroundData.reduce((sum, item) => sum + (item.avg_turnaround_hours || 0), 0) / data.turnaroundData.length).toFixed(1)
                : '--';
            
            const totalValidations = data.turnaroundData 
                ? data.turnaroundData.reduce((sum, item) => sum + (item.total_validations || 0), 0)
                : 0;

            const activeSupervisors = data.turnaroundData 
                ? new Set(data.turnaroundData.map(item => item.supervisor_name)).size
                : 0;

            document.getElementById('avgValidationTime').textContent = avgTime;
            document.getElementById('totalValidations').textContent = totalValidations;
            document.getElementById('validationSuccessRate').textContent = '85%'; // Placeholder
            document.getElementById('activeSupervisors').textContent = activeSupervisors;
        }

        // Create charts
        function createCharts(data) {
            // Destroy existing charts
            if (validationVolumeChart) validationVolumeChart.destroy();
            if (validationTimeChart) validationTimeChart.destroy();

            // Prepare data for charts
            const chartData = prepareChartData(data.turnaroundData);

            // Validation Volume Chart
            const volumeCtx = document.getElementById('validationVolumeChart').getContext('2d');
            validationVolumeChart = new Chart(volumeCtx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'Validations',
                        data: chartData.volumes,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Validation Time Chart
            const timeCtx = document.getElementById('validationTimeChart').getContext('2d');
            validationTimeChart = new Chart(timeCtx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'Avg Time (hours)',
                        data: chartData.times,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Prepare chart data
        function prepareChartData(turnaroundData) {
            if (!turnaroundData || turnaroundData.length === 0) {
                return { labels: [], volumes: [], times: [] };
            }

            // Group by month and aggregate
            const monthlyData = {};
            turnaroundData.forEach(item => {
                const month = new Date(item.validation_month).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
                if (!monthlyData[month]) {
                    monthlyData[month] = { validations: 0, totalTime: 0, count: 0 };
                }
                monthlyData[month].validations += item.total_validations || 0;
                monthlyData[month].totalTime += item.avg_turnaround_hours || 0;
                monthlyData[month].count += 1;
            });

            const labels = Object.keys(monthlyData).sort();
            const volumes = labels.map(label => monthlyData[label].validations);
            const times = labels.map(label => 
                monthlyData[label].count > 0 ? (monthlyData[label].totalTime / monthlyData[label].count).toFixed(1) : 0
            );

            return { labels, volumes, times };
        }

        // Display supervisor performance
        function displaySupervisorPerformance(turnaroundData) {
            const container = document.getElementById('supervisorPerformanceContent');
            
            if (!turnaroundData || turnaroundData.length === 0) {
                container.innerHTML = '<div class="no-data">No supervisor performance data available for the selected period.</div>';
                return;
            }

            let tableHTML = `
                <h3 class="chart-title">Supervisor Performance</h3>
                <table class="trends-table">
                    <thead>
                        <tr>
                            <th>Supervisor</th>
                            <th>Total Validations</th>
                            <th>Avg Turnaround (hours)</th>
                            <th>Performance</th>
                            <th>Period</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            turnaroundData.forEach(supervisor => {
                const performance = getPerformanceIndicator(supervisor.avg_turnaround_hours);
                const period = new Date(supervisor.validation_month).toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
                
                tableHTML += `
                    <tr>
                        <td>${supervisor.supervisor_name || 'Unknown'}</td>
                        <td>${supervisor.total_validations || 0}</td>
                        <td>${supervisor.avg_turnaround_hours ? supervisor.avg_turnaround_hours.toFixed(1) : '--'}</td>
                        <td>
                            <span class="performance-indicator ${performance.class}"></span>
                            ${performance.label}
                        </td>
                        <td>${period}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        // Get performance indicator
        function getPerformanceIndicator(avgHours) {
            if (!avgHours) return { class: 'performance-poor', label: 'No Data' };
            if (avgHours <= 4) return { class: 'performance-excellent', label: 'Excellent' };
            if (avgHours <= 8) return { class: 'performance-good', label: 'Good' };
            return { class: 'performance-poor', label: 'Needs Improvement' };
        }

        // Show error state
        function showError() {
            document.getElementById('supervisorPerformanceContent').innerHTML = 
                '<div class="error">Failed to load validation trends data. Please try again later.</div>';
            
            // Reset metrics
            document.getElementById('avgValidationTime').textContent = 'Error';
            document.getElementById('totalValidations').textContent = 'Error';
            document.getElementById('validationSuccessRate').textContent = 'Error';
            document.getElementById('activeSupervisors').textContent = 'Error';
        }
    </script>
