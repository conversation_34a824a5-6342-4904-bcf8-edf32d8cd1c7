<!-- Head<PERSON> partial for compliance dashboard pages -->
<header class="main-header">
    <nav class="navbar">
        <div class="navbar-brand">
            <a href="/dashboard" class="brand-link">
                <i class="fas fa-clipboard-check"></i>
                <span class="brand-text">Sanitation Checklist System</span>
            </a>
        </div>
        
        <div class="navbar-nav">
            <% if (user) { %>
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-user"></i>
                        <%= user.username %>
                    </a>
                    <div class="dropdown-menu">
                        <a href="/dashboard" class="dropdown-item">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <% if (user.role === 'compliance' || user.role === 'manager' || user.role === 'admin' || user.isAdmin) { %>
                            <a href="/compliance" class="dropdown-item">
                                <i class="fas fa-shield-alt"></i> Compliance Dashboard
                            </a>
                        <% } %>
                        <% if (user.role === 'manager' || user.role === 'admin' || user.isAdmin) { %>
                            <a href="/manager" class="dropdown-item">
                                <i class="fas fa-users-cog"></i> Manager Dashboard
                            </a>
                        <% } %>
                        <div class="dropdown-divider"></div>
                        <a href="/logout" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            <% } else { %>
                <a href="/login-page" class="nav-link">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            <% } %>
        </div>
    </nav>
</header>

<style>
.main-header {
    background: #2c3e50;
    color: white;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.navbar-brand .brand-link {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.navbar-brand .brand-link:hover {
    color: #ecf0f1;
}

.navbar-nav {
    display: flex;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    color: #2c3e50;
    text-decoration: none;
    border-bottom: 1px solid #f8f9fa;
}

.dropdown-item:hover {
    background: #f8f9fa;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-divider {
    height: 1px;
    background: #dee2e6;
    margin: 0.5rem 0;
}

/* Simple dropdown toggle functionality */
.dropdown:hover .dropdown-menu {
    display: block;
}

@media (max-width: 768px) {
    .navbar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .brand-text {
        display: none;
    }
}
</style>

<script>
// Simple dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdown = this.nextElementSibling;
            
            // Close all other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.remove('show');
                }
            });
            
            // Toggle current dropdown
            dropdown.classList.toggle('show');
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
});
</script>
