/**
 * Comprehensive Link Audit Report Generator
 * Creates detailed Excel report with analysis and fix recommendations
 */
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

// Input and output file paths
const resultsDir = path.join(__dirname, 'link-audit-results');
const inputCsvPath = path.join(resultsDir, 'link-audit-results.csv');
const outputXlsxPath = path.join(resultsDir, 'comprehensive-link-audit-report.xlsx');

// Ensure results directory exists
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// Function to create comprehensive Excel report
function createComprehensiveReport() {
  console.log('📊 Generating comprehensive link audit report...');
  
  try {
    // Check if CSV file exists
    if (!fs.existsSync(inputCsvPath)) {
      console.error(`❌ CSV file not found: ${inputCsvPath}`);
      console.log('Please run the link audit first: npm run audit');
      return false;
    }
    
    // Read the CSV file
    console.log('Reading CSV data...');
    const csvData = fs.readFileSync(inputCsvPath, 'utf8');
    
    // Parse CSV data
    const rows = csvData.split('\n');
    const headers = rows[0].split(',').map(h => h.trim());
    
    const jsonData = [];
    for (let i = 1; i < rows.length; i++) {
      if (rows[i].trim() === '') continue;
      
      const values = rows[i].split(',').map(v => v.trim());
      const row = {};
      
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      
      jsonData.push(row);
    }
    
    // Create a new workbook
    const workbook = xlsx.utils.book_new();
    
    // Create summary data
    const summary = {
      total: jsonData.length,
      working: jsonData.filter(row => row['Status'] === 'Working').length,
      broken: jsonData.filter(row => row['Status'] === 'Broken').length,
      byRole: {},
      byDashboard: {}
    };
    
    // Calculate statistics by role
    const roles = [...new Set(jsonData.map(row => row['Role']))];
    roles.forEach(role => {
      const roleData = jsonData.filter(row => row['Role'] === role);
      summary.byRole[role] = {
        total: roleData.length,
        working: roleData.filter(row => row['Status'] === 'Working').length,
        broken: roleData.filter(row => row['Status'] === 'Broken').length
      };
    });
    
    // Calculate statistics by dashboard
    const dashboards = [...new Set(jsonData.map(row => row['Dashboard']))];
    dashboards.forEach(dashboard => {
      const dashboardData = jsonData.filter(row => row['Dashboard'] === dashboard);
      summary.byDashboard[dashboard] = {
        total: dashboardData.length,
        working: dashboardData.filter(row => row['Status'] === 'Working').length,
        broken: dashboardData.filter(row => row['Status'] === 'Broken').length
      };
    });
    
    // Add fix recommendations to broken links
    const enhancedData = jsonData.map(row => {
      if (row['Status'] === 'Broken') {
        const fixDetails = generateFixRecommendation(row);
        return {
          ...row,
          'Proposed Fix': fixDetails.fix,
          'Effort (hours)': fixDetails.effort,
          'Priority': fixDetails.priority
        };
      }
      return row;
    });
    
    // Create main data worksheet
    const mainWorksheet = xlsx.utils.json_to_sheet(enhancedData);
    xlsx.utils.book_append_sheet(workbook, mainWorksheet, 'Link Audit Results');
    
    // Create summary worksheet
    const summaryData = [
      ['Link Audit Summary', ''],
      ['Total Links', summary.total],
      ['Working Links', summary.working],
      ['Broken Links', summary.broken],
      ['Working Percentage', `${((summary.working / summary.total) * 100).toFixed(1)}%`],
      ['', ''],
      ['Results by Role', '']
    ];
    
    // Add role statistics
    Object.keys(summary.byRole).forEach(role => {
      const roleStats = summary.byRole[role];
      summaryData.push(
        [`${role.toUpperCase()} - Total`, roleStats.total],
        [`${role.toUpperCase()} - Working`, roleStats.working],
        [`${role.toUpperCase()} - Broken`, roleStats.broken],
        [`${role.toUpperCase()} - Working Percentage`, `${((roleStats.working / roleStats.total) * 100).toFixed(1)}%`],
        ['', '']
      );
    });
    
    // Add dashboard statistics
    summaryData.push(['Results by Dashboard', '']);
    Object.keys(summary.byDashboard).forEach(dashboard => {
      const dashboardStats = summary.byDashboard[dashboard];
      summaryData.push(
        [`${dashboard} - Total`, dashboardStats.total],
        [`${dashboard} - Working`, dashboardStats.working],
        [`${dashboard} - Broken`, dashboardStats.broken],
        [`${dashboard} - Working Percentage`, `${((dashboardStats.working / dashboardStats.total) * 100).toFixed(1)}%`],
        ['', '']
      );
    });
    
    const summaryWorksheet = xlsx.utils.aoa_to_sheet(summaryData);
    xlsx.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');
    
    // Create broken links worksheet (filtered view)
    const brokenLinks = enhancedData.filter(row => row['Status'] === 'Broken');
    if (brokenLinks.length > 0) {
      const brokenLinksWorksheet = xlsx.utils.json_to_sheet(brokenLinks);
      xlsx.utils.book_append_sheet(workbook, brokenLinksWorksheet, 'Broken Links');
    }
    
    // Create fix plan worksheet
    const fixPlanData = [
      ['Fix Implementation Plan', '', '', '', ''],
      ['Priority', 'Issue', 'Affected Roles', 'Fix Description', 'Estimated Effort'],
      ['HIGH', 'PostgreSQL Dashboard', 'admin', 'Fix route handler and create template', '2-3 hours'],
      ['HIGH', 'HTTP 429 Rate Limiting', 'manager', 'Adjust rate limiting settings in middleware', '1-2 hours'],
      ['MEDIUM', 'Forgot Password Functionality', 'all roles', 'Fix template and route handler for password reset', '2-3 hours'],
      ['', '', '', '', ''],
      ['Implementation Steps:', '', '', '', ''],
      ['1. Fix PostgreSQL Dashboard first', '', '', '', ''],
      ['2. Fix rate limiting issues', '', '', '', ''],
      ['3. Address password reset functionality', '', '', '', ''],
      ['4. Verify fixes with comprehensive re-test', '', '', '', ''],
      ['', '', '', '', ''],
      ['Testing Verification:', '', '', '', ''],
      ['Run the link checker again after implementing fixes to verify all links are working', '', '', '', '']
    ];
    
    const fixPlanWorksheet = xlsx.utils.aoa_to_sheet(fixPlanData);
    xlsx.utils.book_append_sheet(workbook, fixPlanWorksheet, 'Fix Implementation Plan');
    
    // Write to file
    xlsx.writeFile(workbook, outputXlsxPath);
    
    console.log(`✅ Comprehensive report created! Excel file saved to: ${outputXlsxPath}`);
    return true;
  } catch (error) {
    console.error('❌ Error creating comprehensive report:', error);
    console.error(error.stack);
    return false;
  }
}

// Generate fix recommendation based on link data
function generateFixRecommendation(linkData) {
  const url = linkData['URL'] || '';
  const errorDesc = linkData['Error Description'] || '';
  
  // PostgreSQL Dashboard issues
  if (url.includes('/postgresql') || url.includes('/postgresql-dashboard')) {
    return {
      fix: 'Create PostgreSQL dashboard template and fix route handler in admin.js',
      effort: '2-3',
      priority: 'HIGH'
    };
  }
  
  // HTTP 429 Rate Limiting issues
  if (errorDesc.includes('429') || errorDesc.includes('rate limit')) {
    return {
      fix: 'Adjust rate limiting middleware to allow more requests from manager role',
      effort: '1-2',
      priority: 'HIGH'
    };
  }
  
  // Password reset functionality
  if (url.includes('/forgot-password') || url.includes('/reset-password')) {
    return {
      fix: 'Fix the forgot-password template and route handler',
      effort: '2-3',
      priority: 'MEDIUM'
    };
  }
  
  // Default case
  return {
    fix: 'Investigate and fix the route handler and template for this endpoint',
    effort: '1-3',
    priority: 'MEDIUM'
  };
}

// Run the report creation if called directly
if (require.main === module) {
  createComprehensiveReport();
}

module.exports = { createComprehensiveReport };
