<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supervisor Checklist Validation</title>
    <script src="config.js"></script>
    <style>
        /* DHL Unified Stylesheet - Embedded for standalone email page */

        /* Core DHL Styles */
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            margin: 0;
            background: linear-gradient(to bottom, #FFCC00, white);
            color: #333333;
            min-height: 100vh;
            padding: 0;
        }

        /* Container Styles */
        .container, .page-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 2.5rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            box-sizing: border-box;
        }

        /* Header Styles */
        .header {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 30px;
        }

        .logo {
            height: 80px;
            max-width: 90%;
            margin-bottom: 1rem;
        }

        /* Typography */
        h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #D40511; /* DHL Red for the main heading */
            margin: 0 0 1.5rem 0;
            text-align: center;
        }

        h2 {
            color: #333333;
            text-align: center;
            margin: 20px 0;
            font-size: 1.5rem;
        }

        h3 {
            color: #D40511;
            text-align: left;
            margin: 20px 0 10px 0;
            font-size: 1.2rem;
            border-bottom: 1px solid #FFCC00;
            padding-bottom: 5px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-control, input[type="text"] {
            width: 100%;
            padding: 0.75rem;
            font-size: 1rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-control:focus, input[type="text"]:focus {
            outline: none;
            border-color: #FFCC00;
            box-shadow: 0 0 0 2px rgba(255, 204, 0, 0.2);
        }

        .form-label, label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333333;
        }

        /* Button Styles */
        button, .btn {
            display: block;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            box-sizing: border-box;
            width: auto;
            background-color: #FFCC00; /* DHL Yellow */
            color: #333333;
        }

        button:hover, .btn:hover {
            background-color: #D40511; /* DHL Red */
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        button:disabled {
            background-color: #ccc;
            color: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Task Item Styles */
        .task-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .task-item:hover {
            background-color: rgba(255, 204, 0, 0.1);
        }

        .task-item input[type="checkbox"] {
            margin-right: 12px;
            transform: scale(1.2);
        }

        .task-item label {
            margin-bottom: 0;
            cursor: pointer;
            flex: 1;
        }

        /* Message Styles */
        .success {
            color: #28a745;
            background-color: #e9f7ef;
            border: 1px solid #28a745;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .error {
            color: #D40511;
            background-color: #fdecea;
            border: 1px solid #D40511;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .completed {
            color: #6c757d;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        /* Completed checklist styles */
        .completed-checklist {
            opacity: 0.7;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .completed-checklist h3 {
            color: #6c757d;
            border-bottom-color: #dee2e6;
        }

        .completed-checklist .task-item {
            background-color: transparent;
        }

        .completed-checklist .task-item:hover {
            background-color: rgba(108, 117, 125, 0.1);
        }

        .completed-checklist input[type="checkbox"] {
            pointer-events: none;
        }

        .completed-checklist label {
            color: #6c757d;
        }

        .validation-status {
            background-color: #e9ecef;
            border-left: 4px solid #6c757d;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }

        .validation-status h4 {
            margin: 0 0 0.5rem 0;
            color: #495057;
        }

        .validation-status p {
            margin: 0;
            color: #6c757d;
        }

        /* Links */
        a {
            color: #D40511;
            text-decoration: none;
            font-weight: 500;
        }

        a:hover {
            text-decoration: underline;
        }

        /* Loading and status messages */
        #validation-items-container p {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container, .page-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            h1 {
                font-size: 1.8rem;
            }

            h2 {
                font-size: 1.3rem;
            }

            button, .btn {
                padding: 1rem;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <!-- Official DHL Logo embedded for email compatibility -->
            <svg class="logo" id="logo" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 196 51.8368">
                <g>
                    <g>
                        <g>
                            <polygon points="0 22.1001 26.5058 22.1001 25.0581 24.0704 0 24.0704 0 22.1001 0 22.1001" fill="#d40511"/>
                            <polygon points="0 18.4445 29.1986 18.4445 27.7483 20.4121 0 20.4121 0 18.4445 0 18.4445" fill="#d40511"/>
                            <polygon points="0 25.7585 23.8146 25.7585 22.3733 27.7153 0 27.7153 0 25.7585 0 25.7585" fill="#d40511"/>
                        </g>
                        <g>
                            <polygon points="196 24.0704 169.5976 24.0704 171.0446 22.1015 196 22.1015 196 24.0704 196 24.0704" fill="#d40511"/>
                            <polygon points="196 27.7153 166.9128 27.718 168.3537 25.7585 196 25.7585 196 27.7153 196 27.7153" fill="#d40511"/>
                            <polygon points="173.7354 18.4445 196 18.4445 196 20.4134 172.287 20.4148 173.7354 18.4445 173.7354 18.4445" fill="#d40511"/>
                        </g>
                    </g>
                    <g>
                        <path d="m25.6726,27.7153l12.578-17.0886h15.6082c1.7252,0,1.7026.655.8595,1.7975-.8562,1.16-2.3134,3.1547-3.1854,4.3322-.4426.5982-1.2431,1.6881,1.4089,1.6881h20.9101c-1.739,2.3822-7.3796,9.2708-17.5083,9.2708h-30.671Z" fill="#d40511"/>
                        <path d="m97.7056,18.4431l-6.8202,9.2722h-17.9924s6.8172-9.2708,6.8233-9.2708l17.9893-.0014h0Z" fill="#d40511"/>
                        <path d="m123.7239,18.4445l-6.8239,9.2708h-17.9856s6.8178-9.2708,6.8239-9.2708h17.9856Z" fill="#d40511"/>
                        <path d="m129.5749,18.4445c.0006,0-1.314,1.7974-1.9528,2.6604-2.2593,3.0534-.2626,6.6105,7.1109,6.6105h28.8853l6.8226-9.2708h-40.8661Z" fill="#d40511"/>
                    </g>
                    <g>
                        <path d="m34.4685,0l-6.2621,8.5079h34.1288c1.7249,0,1.7023.655.8593,1.7974-.8562,1.1587-2.2894,3.1763-3.1614,4.3539-.4427.5969-1.2431,1.6867,1.4089,1.6867h13.9564s2.2495-3.0614,4.1352-5.6206c2.5655-3.4814.2225-10.7253-8.9486-10.7253h-36.1165Z" fill="#d40511"/>
                        <polygon points="125.2688 16.3458 81.2619 16.3458 93.2956 0 111.2819 0 104.3858 9.3721 112.4136 9.3721 119.3153 0 137.2989 0 125.2688 16.3458 125.2688 16.3458" fill="#d40511"/>
                        <path d="m162.2084,0l-12.0314,16.3458h-19.0593S143.1532,0,143.1592,0h19.0492Z" fill="#d40511"/>
                    </g>
                </g>
                <g>
                    <path d="m45.5383,48.3944c-.971,0-1.863-.1411-2.676-.4227-.813-.2822-1.547-.6658-2.2026-1.1506l1.3768-2.1054c1.075.7745,2.2492,1.1607,3.5214,1.1607.6685,0,1.2161-.1114,1.6422-.3342s.6395-.5767.6395-1.0622c0-.6948-.5179-1.1736-1.5537-1.4362l-1.653-.4328c-1.1148-.3012-1.9568-.7603-2.5274-1.3768s-.8555-1.4295-.8555-2.4396c0-1.2451.449-2.2195,1.3471-2.9211s2.1277-1.052,3.6888-1.052c.8522,0,1.6489.1175,2.3897.3538s1.4389.5834,2.0953,1.0426l-1.3376,2.1243c-.9312-.6685-1.9609-1.0027-3.0885-1.0027-.5638,0-1.0561.1013-1.4754.3045-.42.2039-.6293.5476-.6293,1.0331,0,.3538.1276.6361.3835.8454.2559.21.6395.3808,1.1506.5118l1.6523.4328c1.0885.2883,1.9312.7509,2.5281,1.3869.5962.6361.8947,1.4585.8947,2.4687,0,1.2985-.4686,2.3012-1.4065,3.0095-.9372.7083-2.2391,1.0622-3.9042,1.0622Z" fill="#d40511"/>
                    <path d="m55.9248,48.2965c-.9838,0-1.7381-.3477-2.262-1.0426-.5247-.6955-.7873-1.6786-.7873-2.9508v-6.2358h2.8522v6.0582c0,.5382.1148.9642.3444,1.2789s.5935.472,1.0919.472c.3673,0,.6887-.0885.9642-.2654s.4983-.4031.6685-.6786v-6.8652h2.8522v10.032h-2.6949v-1.7509c-.3018.5773-.7049,1.0459-1.2094,1.4065-.5051.3606-1.1114.5415-1.8198.5415Z" fill="#d40511"/>
                    <path d="m64.2458,51.8368v-13.7695h2.6949v1.7306c.2755-.603.6719-1.075,1.1898-1.416s1.1114-.5118,1.7806-.5118c1.1276,0,2.0095.4301,2.6456,1.2884.6361.8596.9541,2.0365.9541,3.5315v.7867c0,1.495-.318,2.6726-.9541,3.5308-.6361.8596-1.5179,1.289-2.6456,1.289-1.2985,0-2.2364-.6037-2.813-1.8103v5.3506h-2.8522Zm4.5828-11.5668c-.7212,0-1.2978.3086-1.7306.9251v3.7962c.4328.6037,1.0095.9048,1.7306.9048.5902,0,1.0426-.2127,1.3579-.6388.3147-.4261.472-1.0392.472-1.8394v-.6692c0-.7995-.1573-1.4126-.472-1.8394-.3153-.4261-.7677-.6395-1.3579-.6395Z" fill="#d40511"/>
                    <path d="m75.6749,51.8368v-13.7695h2.6949v1.7306c.2755-.603.6719-1.075,1.1898-1.416s1.1114-.5118,1.7806-.5118c1.1276,0,2.0095.4301,2.6456,1.2884.6361.8596.9541,2.0365.9541,3.5315v.7867c0,1.495-.318,2.6726-.9541,3.5308-.6361.8596-1.5179,1.289-2.6456,1.289-1.2985,0-2.2364-.6037-2.813-1.8103v5.3506h-2.8522Zm4.5828-11.5668c-.7212,0-1.2978.3086-1.7306.9251v3.7962c.4328.6037,1.0095.9048,1.7306.9048.5902,0,1.0426-.2127,1.3579-.6388.3147-.4261.472-1.0392.472-1.8394v-.6692c0-.7995-.1573-1.4126-.472-1.8394-.3153-.4261-.7677-.6395-1.3579-.6395Z" fill="#d40511"/>
                    <path d="m90.0939,48.2965c-1.1803,0-1.9839-.266-2.4099-.7968-.4261-.5314-.6395-1.3019-.6395-2.3113v-10.8592h2.8522v10.7214c0,.341.0756.58.2262.7178s.3707.2066.659.2066c.1182,0,.2431-.0128.3741-.0392v2.3012c-.3147.0392-.6692.0594-1.0622.0594Z" fill="#d40511"/>
                    <path d="m94.402,51.8368l1.1999-3.7374h-.9838l-3.0291-10.032h3.1081l1.8292,7.6126h.3342l1.9676-7.6126h3.0089l-4.4647,13.7695h-2.9704Z" fill="#d40511"/>
                    <path d="m112.1844,48.3944c-1.2067,0-2.2458-.2525-3.1183-.7569-.8717-.5051-1.5476-1.2229-2.0257-2.154-.4787-.9312-.7185-2.0331-.7185-3.3053v-1.1405c0-1.2721.2397-2.3735.7185-3.3053.4781-.9312,1.154-1.6489,2.0257-2.154.8724-.5044,1.9116-.7569,3.1183-.7569,1.8225,0,3.331.6624,4.5241,1.9866l-1.7306,1.8691c-.7873-.8657-1.6719-1.2985-2.6557-1.2985-.9575,0-1.6955.3248-2.2134.9737s-.7765,1.5834-.7765,2.8029v.9048c0,1.2073.2552,2.1378.7671,2.7935.5112.6563,1.2458.9838,2.2033.9838.4983,0,.9798-.0986,1.4457-.2951.4652-.1965.9149-.5247,1.3478-.9838l1.7306,1.8691c-.5773.6557-1.2586,1.1479-2.046,1.4754-.7867.3275-1.6523.4916-2.5963.4916Z" fill="#d40511"/>
                    <path d="m118.6167,48.0993v-13.7701h2.8522v5.331c.3147-.5503.7144-.9865,1.1999-1.3079.4848-.3214,1.0689-.4821,1.7509-.4821.9967,0,1.7603.3444,2.2918,1.0331.5307.6881.7968,1.6753.7968,2.9602v6.2358h-2.8522v-6.0589c0-.5375-.1182-.9669-.3545-1.2884-.2357-.3214-.6097-.4821-1.1209-.4821-.3937,0-.7313.0918-1.0135.2755s-.5145.4132-.6982.6887v6.8652h-2.8522Z" fill="#d40511"/>
                    <path d="m132.0722,48.2965c-.8792,0-1.5902-.2721-2.1344-.8164s-.8164-1.2559-.8164-2.1344c0-.9838.3734-1.7637,1.1209-2.3411.7482-.5773,1.7576-.8657,3.0298-.8657h1.574v-.1377c0-1.2586-.6104-1.8886-1.8299-1.8886-.7867,0-1.5078.2694-2.1635.8069l-1.3181-1.7705c.9703-.8785,2.2161-1.3181,3.7374-1.3181,2.8455,0,4.2689,1.3836,4.2689,4.1507v3.1277c0,.5773.2424.8657.7279.8657h.108c.0459,0,.1209-.0068.2262-.0196v2.262c-.1837.0263-.3673.0459-.551.0587-.1837.0135-.3673.0203-.551.0203-.7212,0-1.2755-.135-1.6618-.4038-.3869-.2681-.6395-.6914-.7576-1.2688-.7212,1.1148-1.7246,1.6726-3.0095,1.6726Zm1.1412-2.1054c.6422,0,1.1864-.2816,1.6327-.8454v-1.4362h-1.1803c-1.1412,0-1.7117.4004-1.7117,1.1999,0,.341.1114.607.3342.7968.2228.1904.5314.285.9251.285Z" fill="#d40511"/>
                    <path d="m141.6511,37.0045c-.472,0-.8717-.1377-1.1999-.4126-.3275-.2755-.4916-.6496-.4916-1.1216s.1641-.8461.4916-1.1216c.3282-.2748.7279-.4126,1.1999-.4126.4727,0,.8724.1377,1.2006.4126.3275.2755.4916.6496.4916,1.1216s-.1641.8461-.4916,1.1216c-.3282.2748-.7279.4126-1.2006.4126Zm-1.4356,11.0948v-10.032h2.8522v10.032h-2.8522Z" fill="#d40511"/>
                    <path d="m145.6647,48.0993v-10.032h2.6949v1.7502c.3018-.5767.6982-1.0453,1.1898-1.4065.4922-.3606,1.1047-.5409,1.8394-.5409.9967,0,1.7739.3376,2.3316,1.0135.5571.6752.8359,1.6158.8359,2.8225v6.3932h-2.8522v-6.0589c0-.5375-.1182-.9669-.3545-1.2884-.2357-.3214-.6097-.4821-1.1209-.4821-.3937,0-.7313.0918-1.0135.2755s-.5145.4132-.6982.6887v6.8652h-2.8522Z" fill="#d40511"/>
                </g>
            </svg>
        </header>
        <h1>Supervisor Checklist Validation</h1>
        <h2 id="checklist-title">Loading checklist title...</h2>
        
        <form id="supervisor-validation-form">
            <div id="validation-items-container">
                <p>Loading validation items...</p>
            </div>

            <div class="form-group">
                <label for="supervisorName" class="form-label">Supervisor's Name:</label>
                <input type="text" id="supervisorName" name="supervisorName" class="form-control" required>
            </div>

            <button type="submit" class="btn btn-primary">Submit Validation</button>
        </form>
        <div id="message-area"></div>
        <p style="margin-top: 20px;"><a href="/dashboard">Back to Dashboard</a></p>
	<div class="footer" style="text-align: center; margin-top: 2rem; font-size: 0.8rem; color: #666;">
        &copy; 2025 DHL Supply Chain | Warehouse Sanitation Checklists
        </div>
    </div>
    

    <script>
        // Global variables for JWT and user data (embedded from scripts.js)
        let authToken = null;
        let currentUser = null;

        // Function to fetch JWT from the server (dhl_login) with retry logic
        async function fetchAuthToken(retryCount = 0) {
            const maxRetries = 3;
            try {
                console.log(`[Debug] fetchAuthToken: Attempt ${retryCount + 1}/${maxRetries + 1}`);
                const response = await fetch('/api/auth/issue-jwt-for-session', {
                    method: 'GET',
                    credentials: 'include', // Include cookies for session
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    if (response.status === 401) {
                        console.error('[Debug] fetchAuthToken: Session not authenticated to get JWT (401).');
                        throw new Error('Not authenticated - please log in');
                    } else {
                        console.error(`[Debug] fetchAuthToken: Failed to fetch JWT. Status: ${response.status}`);
                        throw new Error(`HTTP ${response.status}: Failed to fetch authentication token`);
                    }
                }

                const data = await response.json();
                authToken = data.token;
                currentUser = data.user;
                console.log('[Debug] fetchAuthToken: JWT acquired successfully for user:', currentUser.username);
                return true;
            } catch (error) {
                console.error(`[Debug] fetchAuthToken: Error on attempt ${retryCount + 1}:`, error);

                if (retryCount < maxRetries) {
                    console.log(`[Debug] fetchAuthToken: Retrying in 1 second...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return fetchAuthToken(retryCount + 1);
                } else {
                    console.error('[Debug] fetchAuthToken: All retry attempts failed');
                    throw error;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', async () => {
            const checklistTitleEl = document.getElementById('checklist-title');
            const itemsContainerEl = document.getElementById('validation-items-container');
            const validationForm = document.getElementById('supervisor-validation-form');
            const supervisorNameInput = document.getElementById('supervisorName');
            const messageArea = document.getElementById('message-area');

            let checklistId = null;

            function displayMessage(message, type = 'info') {
                messageArea.textContent = message;
                messageArea.className = type; // e.g., 'success', 'error'
            }

            function getChecklistIdFromUrl() {
                const pathParts = window.location.pathname.split('/');
                // Assuming URL is /app/validate-checklist/:id
                if (pathParts.length > 0 && pathParts[pathParts.length - 2] === 'validate-checklist') {
                    return pathParts[pathParts.length - 1];
                }
                return null;
            }

            checklistId = getChecklistIdFromUrl();

            if (!checklistId) {
                checklistTitleEl.textContent = 'Error: Checklist ID not found in URL.';
                itemsContainerEl.innerHTML = '<p style="color: red;">Could not determine the checklist to validate.</p>';
                validationForm.style.display = 'none'; // Hide form if no ID
                return;
            }

            // Ensure authToken is available with robust error handling
            try {
                if (!authToken) {
                    console.log('[Debug] No auth token found, attempting to fetch...');
                    await fetchAuthToken();
                }

                if (!authToken) {
                    throw new Error('Failed to obtain authentication token');
                }

                console.log('[Debug] Authentication successful, proceeding with checklist load');
            } catch (error) {
                console.error('[Debug] Authentication failed:', error);
                checklistTitleEl.textContent = 'Authentication Error';
                itemsContainerEl.innerHTML = `
                    <div class="error">
                        <strong>Authentication Failed:</strong> ${error.message}<br>
                        <p>Please <a href="/login-page">log in</a> and try again.</p>
                        <p>If you continue to have issues, try refreshing the page.</p>
                    </div>
                `;
                validationForm.style.display = 'none';
                return;
            }

            // Fetch checklist data for validation
            try {
                // Wait for configuration to be loaded and get backend API URL
                let backendApiUrl;
                try {
                    if (window.AppConfig && window.AppConfig.waitForConfig) {
                        await window.AppConfig.waitForConfig();
                        backendApiUrl = window.AppConfig.getBackendApiUrl();
                    } else {
                        // Fallback if config module is not available
                        backendApiUrl = 'http://localhost:3001';
                        console.warn('[loadChecklistData] Config module not available, using fallback URL');
                    }
                } catch (error) {
                    console.error('[loadChecklistData] Failed to load configuration:', error);
                    backendApiUrl = 'http://localhost:3001';
                }

                console.log(`[Debug] Fetching checklist data from: ${backendApiUrl}/validate/${checklistId}`);
                const response = await fetch(`${backendApiUrl}/validate/${checklistId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`[Debug] Response status: ${response.status}`);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
                    console.error('[Debug] API Error:', errorData);

                    if (response.status === 401) {
                        throw new Error('Authentication failed. Please log in again.');
                    } else if (response.status === 404) {
                        throw new Error('Checklist not found. The link may be invalid or expired.');
                    } else {
                        throw new Error(errorData.message || `Failed to fetch checklist data. Status: ${response.status}`);
                    }
                }

                const checklistData = await response.json();
                console.log('[Debug] Checklist data received:', {
                    title: checklistData.title,
                    isAlreadyValidated: checklistData.isAlreadyValidated,
                    hasSupervisorValidation: !!checklistData.supervisorValidation
                });

                // Check if checklist is already validated
                if (checklistData.isAlreadyValidated) {
                    console.log('[Debug] Checklist is already validated, showing completion status');
                    checklistTitleEl.textContent = `Checklist Already Completed: ${checklistData.title || 'Untitled Checklist'}`;

                    // Display validation status information
                    const validationInfo = checklistData.supervisorValidation;
                    itemsContainerEl.innerHTML = `
                        <div class="validation-status">
                            <h4>✓ Validation Completed</h4>
                            <p><strong>Validated by:</strong> ${validationInfo.supervisorName}</p>
                            <p><strong>Status:</strong> This checklist has been completed and can no longer be modified.</p>
                        </div>
                        <div class="completed">
                            <strong>Note:</strong> This checklist validation link is no longer active.
                            The checklist has already been reviewed and validated.
                        </div>
                    `;

                    // Hide the validation form
                    validationForm.style.display = 'none';

                    // Show completed checklist items for reference
                    displayCompletedChecklist(checklistData);
                    return;
                }

                console.log('[Debug] Checklist is pending validation, showing form');

                checklistTitleEl.textContent = `Validating: ${checklistData.title || 'Untitled Checklist'}`;

                // Dynamically render validation items (randomCheckboxes)
                itemsContainerEl.innerHTML = ''; // Clear loading message
                if (checklistData.randomCheckboxes && checklistData.randomCheckboxes.length > 0) {
                    const validationItemsByHeading = {}; 
                    // Reconstruct heading structure similar to backend/server.js
                    // This part needs the full checklistData.checkboxes to find labels and original headings
                    checklistData.randomCheckboxes.forEach(checkboxId => {
                        let foundHeading = "Uncategorized Tasks"; // Default
                        let labelText = checkboxId; // Default
                        let originalCheckedState = false;

                        for (const headingKey in checklistData.checkboxes) {
                            if (checklistData.checkboxes[headingKey] && checklistData.checkboxes[headingKey][checkboxId]) {
                                const item = checklistData.checkboxes[headingKey][checkboxId];
                                labelText = item.label || checkboxId;
                                originalCheckedState = item.checked || false; // Use original checked state for pre-population
                                foundHeading = headingKey;
                                break;
                            }
                        }
                        if (!validationItemsByHeading[foundHeading]) {
                            validationItemsByHeading[foundHeading] = [];
                        }
                        validationItemsByHeading[foundHeading].push({ id: checkboxId, label: labelText, checked: originalCheckedState });
                    });

                    for (const heading in validationItemsByHeading) {
                        const headingEl = document.createElement('h3');
                        headingEl.textContent = heading;
                        itemsContainerEl.appendChild(headingEl);

                        validationItemsByHeading[heading].forEach(item => {
                            const div = document.createElement('div');
                            div.className = 'task-item';
                            const checkbox = document.createElement('input');
                            checkbox.type = 'checkbox';
                            checkbox.id = `val-${item.id}`; // Prefix to avoid ID clashes if any
                            checkbox.name = item.id;
                            checkbox.checked = item.checked; // Pre-populate based on original state
                            
                            const label = document.createElement('label');
                            label.htmlFor = checkbox.id;
                            label.textContent = item.label;

                            div.appendChild(checkbox);
                            div.appendChild(label);
                            itemsContainerEl.appendChild(div);
                        });
                    }

                } else {
                    itemsContainerEl.innerHTML = '<p>No items found for validation in this checklist.</p>';
                }

            } catch (error) {
                console.error('Error fetching checklist for validation:', error);
                checklistTitleEl.textContent = 'Error Loading Checklist';
                itemsContainerEl.innerHTML = `<p style="color: red;">${error.message}</p>`;
                validationForm.style.display = 'none';
            }

            // Function to display completed checklist for reference
            function displayCompletedChecklist(checklistData) {
                const completedContainer = document.createElement('div');
                completedContainer.className = 'completed-checklist';
                completedContainer.innerHTML = '<h3>Validation Items (Read-Only)</h3>';

                const validationItemsByHeading = {};
                const validatedCheckboxes = checklistData.supervisorValidation.validatedCheckboxes;

                checklistData.randomCheckboxes.forEach(checkboxId => {
                    let foundHeading = "Uncategorized Tasks";
                    let labelText = checkboxId;
                    let validatedState = validatedCheckboxes[checkboxId] || false;

                    for (const headingKey in checklistData.checkboxes) {
                        if (checklistData.checkboxes[headingKey] && checklistData.checkboxes[headingKey][checkboxId]) {
                            const item = checklistData.checkboxes[headingKey][checkboxId];
                            labelText = item.label || checkboxId;
                            foundHeading = headingKey;
                            break;
                        }
                    }
                    if (!validationItemsByHeading[foundHeading]) {
                        validationItemsByHeading[foundHeading] = [];
                    }
                    validationItemsByHeading[foundHeading].push({
                        id: checkboxId,
                        label: labelText,
                        checked: validatedState
                    });
                });

                for (const heading in validationItemsByHeading) {
                    const headingEl = document.createElement('h4');
                    headingEl.textContent = heading;
                    headingEl.style.color = '#6c757d';
                    completedContainer.appendChild(headingEl);

                    validationItemsByHeading[heading].forEach(item => {
                        const div = document.createElement('div');
                        div.className = 'task-item';
                        const checkbox = document.createElement('input');
                        checkbox.type = 'checkbox';
                        checkbox.checked = item.checked;
                        checkbox.disabled = true;

                        const label = document.createElement('label');
                        label.textContent = item.label;
                        label.style.color = '#6c757d';

                        div.appendChild(checkbox);
                        div.appendChild(label);
                        completedContainer.appendChild(div);
                    });
                }

                itemsContainerEl.appendChild(completedContainer);
            }

            // Handle form submission
            validationForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                const supervisorName = supervisorNameInput.value.trim();
                if (!supervisorName) {
                    displayMessage('Supervisor name is required.', 'error');
                    return;
                }
                if (!authToken) {
                    displayMessage('Authentication error. Please try refreshing.', 'error');
                    return;
                }

                const validatedCheckboxes = [];
                itemsContainerEl.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    validatedCheckboxes.push({
                        id: cb.name, // Original ID stored in name attribute
                        checked: cb.checked
                    });
                });

                try {
                    // Wait for configuration to be loaded and get backend API URL
                    let backendApiUrl;
                    try {
                        if (window.AppConfig && window.AppConfig.waitForConfig) {
                            await window.AppConfig.waitForConfig();
                            backendApiUrl = window.AppConfig.getBackendApiUrl();
                        } else {
                            // Fallback if config module is not available
                            backendApiUrl = 'http://localhost:3001';
                            console.warn('[submitValidation] Config module not available, using fallback URL');
                        }
                    } catch (error) {
                        console.error('[submitValidation] Failed to load configuration:', error);
                        backendApiUrl = 'http://localhost:3001';
                    }

                    const response = await fetch(`${backendApiUrl}/validate/${checklistId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify({
                            supervisorName: supervisorName,
                            validatedCheckboxes: validatedCheckboxes
                        })
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));

                        // Check if the error is due to already validated checklist
                        if (response.status === 400 && errorData.isAlreadyValidated) {
                            displayMessage('This checklist has already been validated by another supervisor and can no longer be modified.', 'completed');
                            validationForm.style.display = 'none';

                            // Show the validation info
                            const validationInfo = errorData.supervisorValidation;
                            if (validationInfo) {
                                const statusDiv = document.createElement('div');
                                statusDiv.className = 'validation-status';
                                statusDiv.innerHTML = `
                                    <h4>✓ Already Validated</h4>
                                    <p><strong>Validated by:</strong> ${validationInfo.supervisorName}</p>
                                    <p><strong>Status:</strong> This checklist was completed while you were reviewing it.</p>
                                `;
                                messageArea.appendChild(statusDiv);
                            }
                            return;
                        }

                        throw new Error(errorData.message || `Failed to submit validation. Status: ${response.status}`);
                    }

                    const result = await response.json();
                    displayMessage(result.message || 'Validation submitted successfully!', 'success');
                    validationForm.style.display = 'none'; // Hide form on success
                    // Redirect to dashboard after a short delay
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1500); // 1.5 second delay to show the success message

                } catch (error) {
                    console.error('Error submitting validation:', error);
                    displayMessage(`Error: ${error.message}`, 'error');
                }
            });
        });
    </script>

    </body>
</html>
