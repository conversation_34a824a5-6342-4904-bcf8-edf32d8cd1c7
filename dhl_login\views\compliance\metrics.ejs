<%- include('../partials/header') %>

<div class="container mt-4">
  <div class="row">
    <div class="col-12">
      <h1 class="page-title">
        <i class="fas fa-chart-line"></i> Compliance Metrics
      </h1>
      <p class="lead">Detailed compliance metrics and validation statistics</p>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Overall Compliance</h5>
        </div>
        <div class="card-body">
          <div id="overallComplianceContent">
            <div class="loading">Loading compliance data...</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Compliance by Checklist Type</h5>
        </div>
        <div class="card-body">
          <div id="checklistTypeContent">
            <div class="loading">Loading checklist data...</div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Daily Compliance Trends</h5>
        </div>
        <div class="card-body">
          <div id="dailyTrendsContent">
            <div class="loading">Loading trend data...</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-4 mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Validation Performance</h5>
        </div>
        <div class="card-body">
          <div id="validationPerformanceContent">
            <div class="loading">Loading validation data...</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-4 mb-4">
    <div class="col-12">
      <div class="actions">
        <a href="/compliance" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Compliance Dashboard
        </a>
      </div>
    </div>
  </div>
</div>

<script>
// Load compliance metrics
async function loadComplianceMetrics() {
  console.log('[Metrics] Loading compliance metrics data');
  
  // Show loading state
  document.getElementById('overallComplianceContent').innerHTML = '<div class="loading">Loading compliance data...</div>';
  document.getElementById('checklistTypeContent').innerHTML = '<div class="loading">Loading checklist data...</div>';
  document.getElementById('dailyTrendsContent').innerHTML = '<div class="loading">Loading trend data...</div>';
  document.getElementById('validationPerformanceContent').innerHTML = '<div class="loading">Loading validation data...</div>';

  try {
    // For now, just display placeholder data
    setTimeout(() => {
      document.getElementById('overallComplianceContent').innerHTML = `
        <div class="metrics-summary">
          <div class="metric-item">
            <div class="metric-value">87%</div>
            <div class="metric-label">Overall Compliance</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">92%</div>
            <div class="metric-label">Validation Rate</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">245</div>
            <div class="metric-label">Total Submissions</div>
          </div>
        </div>
      `;
      
      document.getElementById('checklistTypeContent').innerHTML = `
        <div class="placeholder-chart">
          <p>Checklist compliance data will appear here</p>
          <p class="text-muted">Connect to backend API for real data</p>
        </div>
      `;
      
      document.getElementById('dailyTrendsContent').innerHTML = `
        <div class="placeholder-chart">
          <p>Daily trend data will appear here</p>
          <p class="text-muted">Connect to backend API for real data</p>
        </div>
      `;
      
      document.getElementById('validationPerformanceContent').innerHTML = `
        <div class="placeholder-chart">
          <p>Validation performance data will appear here</p>
          <p class="text-muted">Connect to backend API for real data</p>
        </div>
      `;
    }, 1000);
  } catch (error) {
    console.error('[Metrics] Error loading metrics:', error);
    showMetricsError();
  }
}

// Show error state for metrics
function showMetricsError() {
  const containers = document.querySelectorAll('.card-body');
  containers.forEach(container => {
    container.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <p>Failed to load metrics data. Please try again later.</p>
      </div>
    `;
  });
}

// Load dashboard data on page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('[Metrics] Page loaded');
  loadComplianceMetrics();
});
</script>

<style>
.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.metrics-summary {
  display: flex;
  justify-content: space-around;
  padding: 1rem 0;
}

.metric-item {
  text-align: center;
  padding: 1rem;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #D40511;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
}

.placeholder-chart {
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.error-message {
  text-align: center;
  padding: 2rem;
  color: #D40511;
}

.error-message i {
  font-size: 2rem;
  margin-bottom: 1rem;
}
</style>

</script>
